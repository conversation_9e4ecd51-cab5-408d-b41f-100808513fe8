<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Get search query
$query = sanitizeInput($_GET['q'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 6;
$offset = ($page - 1) * $per_page;

$articles = [];
$total_articles = 0;
$total_pages = 0;

if (!empty($query)) {
    $articles = searchArticles($query, $offset, $per_page);
    
    // Get total count for pagination
    $countSql = "SELECT COUNT(*) as total FROM articles a 
                 WHERE a.status = 'published' 
                 AND (a.title LIKE :query OR a.content LIKE :query OR a.tags LIKE :query)";
    $result = fetchRow($countSql, [':query' => '%' . $query . '%']);
    $total_articles = $result ? $result['total'] : 0;
    $total_pages = ceil($total_articles / $per_page);
}

// Set breadcrumbs
$breadcrumbs = [
    ['title' => 'البحث', 'url' => 'search.php'],
    ['title' => $query ? "نتائج البحث عن: {$query}" : 'البحث']
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $query ? "نتائج البحث عن: {$query}" : 'البحث'; ?> - مدونة عربية</title>
    
    <!-- Meta tags -->
    <meta name="description" content="<?php echo $query ? "نتائج البحث عن {$query} في مدونة عربية" : 'البحث في مدونة عربية'; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- IBM Plex Sans Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-arabic bg-gray-50 text-gray-900">
    
    <!-- Navigation -->
    <?php include 'templates/navigation.php'; ?>
    
    <!-- Search Header -->
    <section class="bg-white py-12 border-b border-gray-200">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto text-center">
                <h1 class="text-3xl md:text-4xl font-bold mb-6">
                    <?php if ($query): ?>
                        نتائج البحث عن: "<?php echo htmlspecialchars($query); ?>"
                    <?php else: ?>
                        البحث في المدونة
                    <?php endif; ?>
                </h1>
                
                <!-- Search Form -->
                <form action="search.php" method="GET" class="flex max-w-lg mx-auto">
                    <input type="text" 
                           name="q" 
                           value="<?php echo htmlspecialchars($query); ?>"
                           placeholder="ابحث عن مقال..." 
                           class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                           required>
                    <button type="submit" 
                            class="bg-blue-600 text-white px-6 py-3 rounded-l-lg hover:bg-blue-700 transition duration-300">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </form>
                
                <?php if ($query && $total_articles > 0): ?>
                    <p class="text-gray-600 mt-4">
                        تم العثور على <?php echo number_format($total_articles); ?> نتيجة
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </section>
    
    <!-- Search Results -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            
            <?php if (empty($query)): ?>
                <!-- Search Tips -->
                <div class="max-w-2xl mx-auto">
                    <div class="bg-white rounded-lg shadow-lg p-8 text-center">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <h2 class="text-2xl font-bold mb-4">ابحث في مدونتنا</h2>
                        <p class="text-gray-600 mb-6">استخدم مربع البحث أعلاه للعثور على المقالات التي تهمك</p>
                        
                        <div class="text-right">
                            <h3 class="font-semibold mb-3">نصائح للبحث:</h3>
                            <ul class="text-gray-600 space-y-2">
                                <li>• استخدم كلمات مفتاحية واضحة</li>
                                <li>• جرب البحث بكلمات مختلفة</li>
                                <li>• يمكنك البحث في العناوين والمحتوى والوسوم</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Popular Tags -->
                    <div class="mt-8">
                        <h3 class="text-xl font-bold mb-4 text-center">الوسوم الشائعة</h3>
                        <div class="flex flex-wrap justify-center gap-3">
                            <?php 
                            $popularTags = getPopularTags(10);
                            foreach ($popularTags as $tag => $count): 
                            ?>
                                <a href="search.php?q=<?php echo urlencode($tag); ?>" 
                                   class="bg-blue-100 text-blue-800 px-4 py-2 rounded-full hover:bg-blue-200 transition duration-300">
                                    <?php echo htmlspecialchars($tag); ?>
                                    <span class="text-blue-600 text-sm">(<?php echo $count; ?>)</span>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
            <?php elseif (empty($articles)): ?>
                <!-- No Results -->
                <div class="max-w-2xl mx-auto text-center">
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h2 class="text-2xl font-bold mb-4">لم يتم العثور على نتائج</h2>
                        <p class="text-gray-600 mb-6">
                            لم نجد أي مقالات تحتوي على "<?php echo htmlspecialchars($query); ?>"
                        </p>
                        
                        <div class="space-y-4">
                            <p class="text-gray-600">جرب:</p>
                            <ul class="text-gray-600 space-y-2">
                                <li>• التأكد من الإملاء</li>
                                <li>• استخدام كلمات أخرى</li>
                                <li>• البحث بكلمات أكثر عمومية</li>
                            </ul>
                        </div>
                        
                        <div class="mt-6">
                            <a href="index.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300">
                                تصفح جميع المقالات
                            </a>
                        </div>
                    </div>
                </div>
                
            <?php else: ?>
                <!-- Search Results -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($articles as $article): ?>
                        <article class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
                            <?php if ($article['image']): ?>
                                <img src="uploads/<?php echo htmlspecialchars($article['image']); ?>" 
                                     alt="<?php echo htmlspecialchars($article['title']); ?>" 
                                     class="w-full h-48 object-cover">
                            <?php endif; ?>
                            
                            <div class="p-6">
                                <h3 class="text-xl font-bold mb-3 hover:text-blue-600 transition duration-300">
                                    <a href="article.php?id=<?php echo $article['id']; ?>">
                                        <?php 
                                        // Highlight search terms in title
                                        $highlightedTitle = str_ireplace($query, "<mark class='bg-yellow-200'>$query</mark>", htmlspecialchars($article['title']));
                                        echo $highlightedTitle;
                                        ?>
                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 mb-4 leading-loose">
                                    <?php 
                                    $excerpt = getExcerpt($article['content'], 150);
                                    // Highlight search terms in excerpt
                                    $highlightedExcerpt = str_ireplace($query, "<mark class='bg-yellow-200'>$query</mark>", htmlspecialchars($excerpt));
                                    echo $highlightedExcerpt;
                                    ?>
                                </p>
                                
                                <?php if ($article['tags']): ?>
                                    <div class="flex flex-wrap gap-2 mb-4">
                                        <?php foreach (explode(',', $article['tags']) as $tag): ?>
                                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                                                <?php echo htmlspecialchars(trim($tag)); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="flex justify-between items-center text-sm text-gray-500">
                                    <span><?php echo formatArabicDate($article['created_at']); ?></span>
                                    <a href="article.php?id=<?php echo $article['id']; ?>" 
                                       class="text-blue-600 hover:text-blue-800 font-semibold">
                                        اقرأ المزيد ←
                                    </a>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="flex justify-center mt-12">
                        <nav class="flex space-x-2 space-x-reverse">
                            <?php if ($page > 1): ?>
                                <a href="?q=<?php echo urlencode($query); ?>&page=<?php echo $page - 1; ?>" 
                                   class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                                    السابق
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <a href="?q=<?php echo urlencode($query); ?>&page=<?php echo $i; ?>" 
                                   class="px-4 py-2 <?php echo $i == $page ? 'bg-blue-600 text-white' : 'bg-white border border-gray-300 hover:bg-gray-50'; ?> rounded-lg">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?q=<?php echo urlencode($query); ?>&page=<?php echo $page + 1; ?>" 
                                   class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                                    التالي
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                <?php endif; ?>
                
            <?php endif; ?>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
