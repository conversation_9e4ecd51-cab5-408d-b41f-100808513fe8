<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Get article ID
$article_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$article_id) {
    header('Location: index.php');
    exit;
}

// Get article details and increment view count
$article = getPublishedArticleById($article_id);

if (!$article) {
    header('Location: index.php');
    exit;
}

// Get related articles
$related_articles = getRelatedArticles($article_id, $article['tags'], 3);

// Set breadcrumbs
$breadcrumbs = [
    ['title' => $article['title']]
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($article['title']); ?> - مدونة عربية</title>
    
    <!-- Meta tags for social sharing -->
    <meta name="description" content="<?php echo htmlspecialchars(getExcerpt($article['content'], 160)); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($article['tags']); ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars(getExcerpt($article['content'], 160)); ?>">
    <meta property="og:url" content="<?php echo getCurrentUrl(); ?>">
    <meta property="og:type" content="article">
    <?php if ($article['image']): ?>
        <meta property="og:image" content="<?php echo getBaseUrl(); ?>/uploads/<?php echo htmlspecialchars($article['image']); ?>">
    <?php endif; ?>
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars(getExcerpt($article['content'], 160)); ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- IBM Plex Sans Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-arabic bg-gray-50 text-gray-900">
    
    <!-- Navigation -->
    <?php include 'templates/navigation.php'; ?>
    
    <!-- Article Content -->
    <article class="py-12">
        <div class="container mx-auto px-4 max-w-4xl">
            
            <!-- Article Header -->
            <header class="mb-8">
                <h1 class="text-3xl md:text-5xl font-bold mb-6 leading-tight">
                    <?php echo htmlspecialchars($article['title']); ?>
                </h1>
                
                <div class="flex flex-wrap items-center gap-4 text-gray-600 mb-6">
                    <span class="flex items-center gap-2">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        <?php echo formatArabicDate($article['created_at']); ?>
                    </span>
                    
                    <span class="flex items-center gap-2">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                        </svg>
                        <?php echo number_format($article['views']); ?> مشاهدة
                    </span>
                </div>
                
                <?php if ($article['tags']): ?>
                    <div class="flex flex-wrap gap-2 mb-6">
                        <?php foreach (explode(',', $article['tags']) as $tag): ?>
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                                <?php echo htmlspecialchars(trim($tag)); ?>
                            </span>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </header>
            
            <!-- Article Image -->
            <?php if ($article['image']): ?>
                <div class="mb-8">
                    <img src="uploads/<?php echo htmlspecialchars($article['image']); ?>" 
                         alt="<?php echo htmlspecialchars($article['title']); ?>" 
                         class="w-full rounded-lg shadow-lg">
                </div>
            <?php endif; ?>
            
            <!-- Article Content -->
            <div class="prose prose-lg max-w-none mb-12 leading-loose">
                <?php echo $article['content']; ?>
            </div>
            
            <!-- Share Buttons -->
            <div class="border-t border-gray-200 pt-8 mb-12">
                <h3 class="text-xl font-bold mb-4">شارك المقال</h3>
                <div class="flex flex-wrap gap-4">
                    <a href="https://twitter.com/intent/tweet?text=<?php echo urlencode($article['title']); ?>&url=<?php echo urlencode(getCurrentUrl()); ?>" 
                       target="_blank" 
                       class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition duration-300 flex items-center gap-2">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                        تويتر
                    </a>
                    
                    <a href="https://wa.me/?text=<?php echo urlencode($article['title'] . ' - ' . getCurrentUrl()); ?>" 
                       target="_blank" 
                       class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition duration-300 flex items-center gap-2">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        واتساب
                    </a>
                    
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(getCurrentUrl()); ?>" 
                       target="_blank" 
                       class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300 flex items-center gap-2">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        فيسبوك
                    </a>
                </div>
            </div>
        </div>
    </article>
    
    <!-- Related Articles -->
    <?php if (!empty($related_articles)): ?>
        <section class="bg-white py-16">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12">مقالات ذات صلة</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <?php foreach ($related_articles as $related): ?>
                        <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition duration-300">
                            <?php if ($related['image']): ?>
                                <img src="uploads/<?php echo htmlspecialchars($related['image']); ?>" 
                                     alt="<?php echo htmlspecialchars($related['title']); ?>" 
                                     class="w-full h-40 object-cover">
                            <?php endif; ?>
                            
                            <div class="p-6">
                                <h3 class="text-lg font-bold mb-3 hover:text-blue-600 transition duration-300">
                                    <a href="article.php?id=<?php echo $related['id']; ?>">
                                        <?php echo htmlspecialchars($related['title']); ?>
                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 text-sm mb-4">
                                    <?php echo htmlspecialchars(getExcerpt($related['content'], 100)); ?>
                                </p>
                                
                                <a href="article.php?id=<?php echo $related['id']; ?>" 
                                   class="text-blue-600 hover:text-blue-800 text-sm font-semibold">
                                    اقرأ المزيد ←
                                </a>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
