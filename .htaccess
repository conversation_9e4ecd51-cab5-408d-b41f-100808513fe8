# Arabic Blog .htaccess Configuration
# Security and Performance Settings

# Enable Rewrite Engine
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed)
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.tiny.cloud; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:; connect-src 'self';"
</IfModule>

# Hide sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files "config/database.php">
    Order allow,deny
    <PERSON>y from all
</Files>

<Files "*.sql">
    Order allow,deny
    <PERSON>y from all
</Files>

<Files "composer.json">
    Order allow,deny
    <PERSON>y from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>

<Files "README.md">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent PHP execution in uploads directory
<Directory "uploads">
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
</Directory>

# Custom Error Pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# URL Rewriting for SEO-friendly URLs
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Article URLs: /article/123/title -> article.php?id=123
RewriteRule ^article/([0-9]+)/?([^/]*)?/?$ article.php?id=$1 [L,QSA]

# Tag URLs: /tag/technology -> index.php?tag=technology
RewriteRule ^tag/([^/]+)/?$ index.php?tag=$1 [L,QSA]

# Search URLs: /search/query -> search.php?q=query
RewriteRule ^search/([^/]+)/?$ search.php?q=$1 [L,QSA]

# Admin URLs protection
RewriteCond %{REQUEST_URI} ^/admin/
RewriteCond %{REQUEST_URI} !^/admin/login\.php$
RewriteCond %{REQUEST_URI} !^/admin/logout\.php$
RewriteCond %{REQUEST_URI} !^/admin/assets/
RewriteRule ^admin/ admin/login.php [R=302,L]

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Prevent hotlinking
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [NC,F,L]

# Limit file upload size (adjust as needed)
php_value upload_max_filesize 5M
php_value post_max_size 5M

# Set timezone (adjust as needed)
php_value date.timezone "Asia/Riyadh"

# Disable server signature
ServerSignature Off

# Prevent access to version control directories
RedirectMatch 404 /\.git

# Force HTTPS (uncomment if you have SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Prevent access to sensitive directories
RedirectMatch 403 ^/config/
RedirectMatch 403 ^/includes/
RedirectMatch 403 ^/vendor/

# Rate limiting (if mod_evasive is available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
