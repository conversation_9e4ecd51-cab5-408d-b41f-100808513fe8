<?php
/**
 * Navigation Template
 * Responsive RTL navigation with hamburger menu
 */
?>
<nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center py-4">
            
            <!-- Logo -->
            <div class="flex items-center">
                <a href="index.php" class="text-2xl font-bold text-blue-600 hover:text-blue-800 transition duration-300">
                    <?php echo getSiteSetting('site_name', 'مدونة عربية'); ?>
                </a>
            </div>
            
            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8 space-x-reverse">
                <a href="index.php" class="text-gray-700 hover:text-blue-600 font-medium transition duration-300 <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'text-blue-600' : ''; ?>">
                    الرئيسية
                </a>
                
                <!-- Categories Dropdown -->
                <div class="relative group">
                    <button class="text-gray-700 hover:text-blue-600 font-medium transition duration-300 flex items-center">
                        التصنيفات
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                        <div class="py-2">
                            <a href="index.php?tag=تقنية" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition duration-300">تقنية</a>
                            <a href="index.php?tag=برمجة" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition duration-300">برمجة</a>
                            <a href="index.php?tag=رأي" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition duration-300">رأي</a>
                            <a href="index.php?tag=أخبار" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition duration-300">أخبار</a>
                        </div>
                    </div>
                </div>
                
                <a href="contact.php" class="text-gray-700 hover:text-blue-600 font-medium transition duration-300 <?php echo basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'text-blue-600' : ''; ?>">
                    اتصل بنا
                </a>
                
                <!-- Search -->
                <div class="relative">
                    <form action="search.php" method="GET" class="flex">
                        <input type="text" 
                               name="q" 
                               placeholder="البحث..." 
                               value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>"
                               class="px-4 py-2 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right w-64">
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-l-lg hover:bg-blue-700 transition duration-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Mobile Menu Button -->
            <div class="md:hidden">
                <button id="mobile-menu-button" class="text-gray-700 hover:text-blue-600 focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden pb-4">
            <div class="space-y-2">
                <a href="index.php" class="block py-2 text-gray-700 hover:text-blue-600 font-medium transition duration-300 <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'text-blue-600' : ''; ?>">
                    الرئيسية
                </a>
                
                <!-- Mobile Categories -->
                <div class="py-2">
                    <div class="text-gray-700 font-medium mb-2">التصنيفات</div>
                    <div class="pr-4 space-y-1">
                        <a href="index.php?tag=تقنية" class="block py-1 text-gray-600 hover:text-blue-600 transition duration-300">تقنية</a>
                        <a href="index.php?tag=برمجة" class="block py-1 text-gray-600 hover:text-blue-600 transition duration-300">برمجة</a>
                        <a href="index.php?tag=رأي" class="block py-1 text-gray-600 hover:text-blue-600 transition duration-300">رأي</a>
                        <a href="index.php?tag=أخبار" class="block py-1 text-gray-600 hover:text-blue-600 transition duration-300">أخبار</a>
                    </div>
                </div>
                
                <a href="contact.php" class="block py-2 text-gray-700 hover:text-blue-600 font-medium transition duration-300 <?php echo basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'text-blue-600' : ''; ?>">
                    اتصل بنا
                </a>
                
                <!-- Mobile Search -->
                <div class="pt-2">
                    <form action="search.php" method="GET" class="flex">
                        <input type="text" 
                               name="q" 
                               placeholder="البحث..." 
                               value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>"
                               class="flex-1 px-4 py-2 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right">
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-l-lg hover:bg-blue-700 transition duration-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Breadcrumb (optional, for inner pages) -->
<?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
<nav class="bg-gray-100 py-3">
    <div class="container mx-auto px-4">
        <ol class="flex items-center space-x-2 space-x-reverse text-sm">
            <li>
                <a href="index.php" class="text-blue-600 hover:text-blue-800">الرئيسية</a>
            </li>
            <?php foreach ($breadcrumbs as $breadcrumb): ?>
                <li class="flex items-center">
                    <svg class="w-4 h-4 text-gray-400 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    <?php if (isset($breadcrumb['url'])): ?>
                        <a href="<?php echo $breadcrumb['url']; ?>" class="text-blue-600 hover:text-blue-800">
                            <?php echo htmlspecialchars($breadcrumb['title']); ?>
                        </a>
                    <?php else: ?>
                        <span class="text-gray-600"><?php echo htmlspecialchars($breadcrumb['title']); ?></span>
                    <?php endif; ?>
                </li>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif; ?>

<script>
// Mobile menu toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
});
</script>
