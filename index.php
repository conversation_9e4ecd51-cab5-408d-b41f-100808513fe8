<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Get page number for pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 6;
$offset = ($page - 1) * $per_page;

// Get published articles
$articles = getPublishedArticles($offset, $per_page);
$total_articles = getTotalPublishedArticles();
$total_pages = ceil($total_articles / $per_page);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدونة عربية - الصفحة الرئيسية</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- IBM Plex Sans Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-arabic bg-gray-50 text-gray-900">
    
    <!-- Navigation -->
    <?php include 'templates/navigation.php'; ?>
    
    <!-- Hero Section -->
    <section class="bg-gradient-to-l from-blue-600 to-purple-700 text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">مرحباً بكم في مدونتنا العربية</h1>
            <p class="text-xl md:text-2xl mb-8 leading-loose">اكتشف أحدث المقالات والأفكار في عالم التقنية والبرمجة</p>
            <a href="#articles" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300">
                تصفح المقالات
            </a>
        </div>
    </section>
    
    <!-- Articles Section -->
    <section id="articles" class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">أحدث المقالات</h2>
            
            <?php if (empty($articles)): ?>
                <div class="text-center py-12">
                    <p class="text-xl text-gray-600">لا توجد مقالات منشورة حالياً</p>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($articles as $article): ?>
                        <article class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
                            <?php if ($article['image']): ?>
                                <img src="uploads/<?php echo htmlspecialchars($article['image']); ?>" 
                                     alt="<?php echo htmlspecialchars($article['title']); ?>" 
                                     class="w-full h-48 object-cover">
                            <?php endif; ?>
                            
                            <div class="p-6">
                                <h3 class="text-xl font-bold mb-3 hover:text-blue-600 transition duration-300">
                                    <a href="article.php?id=<?php echo $article['id']; ?>">
                                        <?php echo htmlspecialchars($article['title']); ?>
                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 mb-4 leading-loose">
                                    <?php echo htmlspecialchars(getExcerpt($article['content'], 150)); ?>
                                </p>
                                
                                <?php if ($article['tags']): ?>
                                    <div class="flex flex-wrap gap-2 mb-4">
                                        <?php foreach (explode(',', $article['tags']) as $tag): ?>
                                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                                                <?php echo htmlspecialchars(trim($tag)); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="flex justify-between items-center text-sm text-gray-500">
                                    <span><?php echo formatArabicDate($article['created_at']); ?></span>
                                    <a href="article.php?id=<?php echo $article['id']; ?>" 
                                       class="text-blue-600 hover:text-blue-800 font-semibold">
                                        اقرأ المزيد ←
                                    </a>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="flex justify-center mt-12">
                        <nav class="flex space-x-2 space-x-reverse">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>" 
                                   class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                                    السابق
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <a href="?page=<?php echo $i; ?>" 
                                   class="px-4 py-2 <?php echo $i == $page ? 'bg-blue-600 text-white' : 'bg-white border border-gray-300 hover:bg-gray-50'; ?> rounded-lg">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>" 
                                   class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                                    التالي
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
