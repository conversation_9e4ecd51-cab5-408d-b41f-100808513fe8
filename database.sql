-- Arabic Blog Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS arabic_blog CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE arabic_blog;

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Articles table
CREATE TABLE IF NOT EXISTS articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content LONGTEXT NOT NULL,
    excerpt TEXT,
    image VARCHAR(255),
    tags TEXT,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    views INT DEFAULT 0,
    author_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL,
    
    FOREIGN KEY (author_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_published_at (published_at),
    FULLTEXT KEY ft_title_content (title, content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Categories table (optional enhancement)
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Article categories junction table
CREATE TABLE IF NOT EXISTS article_categories (
    article_id INT NOT NULL,
    category_id INT NOT NULL,
    PRIMARY KEY (article_id, category_id),
    FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Settings table for site configuration
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, password, email, full_name) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'مدير الموقع');

-- Insert default categories
INSERT INTO categories (name, slug, description) VALUES 
('تقنية', 'technology', 'مقالات حول التقنية والتكنولوجيا'),
('برمجة', 'programming', 'مقالات حول البرمجة وتطوير البرمجيات'),
('رأي', 'opinion', 'مقالات رأي وتحليلات'),
('أخبار', 'news', 'آخر الأخبار والمستجدات');

-- Insert default settings
INSERT INTO settings (setting_key, setting_value) VALUES 
('site_name', 'مدونة عربية'),
('site_description', 'مدونة عربية متخصصة في التقنية والبرمجة'),
('site_keywords', 'مدونة, عربية, تقنية, برمجة'),
('posts_per_page', '6'),
('site_email', '<EMAIL>'),
('social_twitter', 'https://twitter.com/example'),
('social_facebook', 'https://facebook.com/example'),
('social_instagram', 'https://instagram.com/example');

-- Insert sample articles
INSERT INTO articles (title, content, excerpt, tags, status, author_id, published_at) VALUES 
(
    'مرحباً بكم في مدونتنا العربية الجديدة',
    '<p>نرحب بكم في مدونتنا العربية الجديدة المتخصصة في التقنية والبرمجة. هذه المدونة تهدف إلى تقديم محتوى عربي عالي الجودة في مجال التكنولوجيا.</p>
    <p>سنقوم بنشر مقالات متنوعة تشمل:</p>
    <ul>
        <li>أحدث التقنيات والأدوات</li>
        <li>دروس البرمجة والتطوير</li>
        <li>نصائح وحيل تقنية</li>
        <li>مراجعات للمنتجات التقنية</li>
    </ul>
    <p>نتطلع إلى مشاركتكم وتفاعلكم معنا في هذه الرحلة التقنية الممتعة.</p>',
    'نرحب بكم في مدونتنا العربية الجديدة المتخصصة في التقنية والبرمجة. هذه المدونة تهدف إلى تقديم محتوى عربي عالي الجودة في مجال التكنولوجيا.',
    'مرحبا, مدونة, تقنية, برمجة',
    'published',
    1,
    NOW()
),
(
    'أساسيات البرمجة للمبتدئين',
    '<p>البرمجة هي فن وعلم كتابة التعليمات للحاسوب لحل المشاكل وإنجاز المهام. في هذا المقال، سنتعرف على أساسيات البرمجة التي يحتاجها كل مبتدئ.</p>
    <h2>ما هي البرمجة؟</h2>
    <p>البرمجة هي عملية إنشاء مجموعة من التعليمات التي تخبر الحاسوب كيفية أداء مهمة معينة. هذه التعليمات تُكتب بلغة يفهمها الحاسوب تسمى "لغة البرمجة".</p>
    <h2>لغات البرمجة الشائعة</h2>
    <ul>
        <li>Python - سهلة التعلم ومتعددة الاستخدامات</li>
        <li>JavaScript - لغة الويب الأساسية</li>
        <li>Java - قوية ومستخدمة في التطبيقات الكبيرة</li>
        <li>C++ - سريعة ومناسبة للألعاب والأنظمة</li>
    </ul>
    <p>ننصح المبتدئين بالبدء بلغة Python لسهولتها وقوتها.</p>',
    'البرمجة هي فن وعلم كتابة التعليمات للحاسوب لحل المشاكل وإنجاز المهام. في هذا المقال، سنتعرف على أساسيات البرمجة التي يحتاجها كل مبتدئ.',
    'برمجة, مبتدئين, تعلم, Python, JavaScript',
    'published',
    1,
    NOW() - INTERVAL 1 DAY
),
(
    'مستقبل الذكاء الاصطناعي',
    '<p>الذكاء الاصطناعي يشهد تطوراً مذهلاً في السنوات الأخيرة، ويؤثر على جميع جوانب حياتنا. في هذا المقال، نستكشف مستقبل هذه التقنية الثورية.</p>
    <h2>التطبيقات الحالية</h2>
    <p>يُستخدم الذكاء الاصطناعي حالياً في:</p>
    <ul>
        <li>المساعدات الصوتية مثل Siri و Alexa</li>
        <li>أنظمة التوصية في Netflix و YouTube</li>
        <li>السيارات ذاتية القيادة</li>
        <li>التشخيص الطبي</li>
    </ul>
    <h2>التحديات والفرص</h2>
    <p>رغم الإمكانيات الهائلة، يواجه الذكاء الاصطناعي تحديات أخلاقية وتقنية يجب التعامل معها بحكمة.</p>',
    'الذكاء الاصطناعي يشهد تطوراً مذهلاً في السنوات الأخيرة، ويؤثر على جميع جوانب حياتنا. في هذا المقال، نستكشف مستقبل هذه التقنية الثورية.',
    'ذكاء اصطناعي, مستقبل, تقنية, تطوير',
    'published',
    1,
    NOW() - INTERVAL 2 DAY
);

-- Create indexes for better performance
CREATE INDEX idx_articles_status_published ON articles (status, published_at DESC);
CREATE INDEX idx_articles_tags ON articles (tags(100));
