<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدونة عربية - الصفحة الرئيسية</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- IBM Plex Sans Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-arabic bg-gray-50 text-gray-900">
    
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="index.php" class="text-2xl font-bold text-blue-600 hover:text-blue-800 transition duration-300">
                        مدونة عربية
                    </a>
                </div>
                
                <div class="hidden md:flex items-center space-x-8 space-x-reverse">
                    <a href="index.php" class="text-gray-700 hover:text-blue-600 font-medium transition duration-300">
                        الرئيسية
                    </a>
                    <a href="contact.php" class="text-gray-700 hover:text-blue-600 font-medium transition duration-300">
                        اتصل بنا
                    </a>
                    <a href="admin/login.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-300">
                        لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <section class="bg-gradient-to-l from-blue-600 to-purple-700 text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">مرحباً بكم في مدونتنا العربية</h1>
            <p class="text-xl md:text-2xl mb-8 leading-loose">اكتشف أحدث المقالات والأفكار في عالم التقنية والبرمجة</p>
            <a href="#setup" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300">
                إعداد النظام
            </a>
        </div>
    </section>
    
    <!-- Setup Instructions -->
    <section id="setup" class="py-16">
        <div class="container mx-auto px-4 max-w-4xl">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">إعداد النظام</h2>
            
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600">خطوات الإعداد</h3>
                
                <div class="space-y-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold mr-4">
                            1
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold mb-2">إنشاء قاعدة البيانات</h4>
                            <p class="text-gray-600 mb-2">قم بإنشاء قاعدة بيانات MySQL جديدة واستيراد ملف <code class="bg-gray-100 px-2 py-1 rounded">database.sql</code></p>
                            <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto">mysql -u username -p database_name &lt; database.sql</pre>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold mr-4">
                            2
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold mb-2">تكوين قاعدة البيانات</h4>
                            <p class="text-gray-600 mb-2">حرر ملف <code class="bg-gray-100 px-2 py-1 rounded">config/database.php</code> وأدخل معلومات قاعدة البيانات:</p>
                            <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto">define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');</pre>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold mr-4">
                            3
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold mb-2">إنشاء المجلدات المطلوبة</h4>
                            <p class="text-gray-600 mb-2">تأكد من وجود المجلدات التالية مع الصلاحيات المناسبة:</p>
                            <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto">mkdir uploads logs
chmod 755 uploads logs</pre>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold mr-4">
                            4
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold mb-2">اختبار النظام</h4>
                            <p class="text-gray-600 mb-2">قم بزيارة الروابط التالية للتأكد من عمل النظام:</p>
                            <div class="space-y-2">
                                <a href="debug.php" class="inline-block bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 transition duration-300">
                                    صفحة التشخيص
                                </a>
                                <a href="test.php" class="inline-block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition duration-300">
                                    اختبار النظام
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Default Login Info -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h3 class="text-xl font-bold mb-4 text-blue-800">معلومات تسجيل الدخول الافتراضية</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <strong>الرابط:</strong> <a href="admin/login.php" class="text-blue-600 hover:underline">admin/login.php</a>
                    </div>
                    <div>
                        <strong>اسم المستخدم:</strong> <code class="bg-white px-2 py-1 rounded">admin</code>
                    </div>
                    <div>
                        <strong>كلمة المرور:</strong> <code class="bg-white px-2 py-1 rounded">admin123</code>
                    </div>
                    <div class="text-red-600 font-semibold">
                        ⚠️ غيّر كلمة المرور فوراً بعد أول تسجيل دخول!
                    </div>
                </div>
            </div>
            
            <!-- Troubleshooting -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 class="text-xl font-bold mb-4 text-red-800">استكشاف الأخطاء</h3>
                <div class="space-y-3 text-red-700">
                    <p><strong>خطأ 500:</strong> تحقق من إعدادات قاعدة البيانات وتأكد من استيراد ملف database.sql</p>
                    <p><strong>خطأ في الاتصال:</strong> تأكد من تشغيل خادم MySQL وصحة معلومات الاتصال</p>
                    <p><strong>مشاكل الصلاحيات:</strong> تأكد من أن مجلدات uploads و logs قابلة للكتابة</p>
                    <p><strong>مشاكل الخط:</strong> تأكد من اتصال الإنترنت لتحميل خط IBM Plex Sans Arabic</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 مدونة عربية. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
</body>
</html>
