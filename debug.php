<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Information</h1>";

// Test 1: Basic PHP
echo "<h2>1. PHP Version</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";

// Test 2: Check if files exist
echo "<h2>2. File Existence Check</h2>";
$files = [
    'config/database.php',
    'includes/functions.php',
    'includes/security.php',
    'templates/navigation.php',
    'templates/footer.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// Test 3: Try to include config
echo "<h2>3. Database Configuration Test</h2>";
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        echo "✅ Database config loaded<br>";
        
        // Test database connection
        if (isset($pdo)) {
            echo "✅ PDO connection exists<br>";
            
            // Test a simple query
            try {
                $stmt = $pdo->query("SELECT 1");
                echo "✅ Database connection working<br>";
            } catch (Exception $e) {
                echo "❌ Database query failed: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ PDO connection not found<br>";
        }
    } else {
        echo "❌ Database config file missing<br>";
    }
} catch (Exception $e) {
    echo "❌ Database config error: " . $e->getMessage() . "<br>";
}

// Test 4: Try to include functions
echo "<h2>4. Functions Test</h2>";
try {
    if (file_exists('includes/functions.php')) {
        require_once 'includes/functions.php';
        echo "✅ Functions file loaded<br>";
        
        // Test if key functions exist
        $functions = ['getPublishedArticles', 'getTotalPublishedArticles', 'formatArabicDate', 'getExcerpt'];
        foreach ($functions as $func) {
            if (function_exists($func)) {
                echo "✅ Function $func exists<br>";
            } else {
                echo "❌ Function $func missing<br>";
            }
        }
    } else {
        echo "❌ Functions file missing<br>";
    }
} catch (Exception $e) {
    echo "❌ Functions error: " . $e->getMessage() . "<br>";
}

// Test 5: Directory permissions
echo "<h2>5. Directory Permissions</h2>";
$dirs = ['uploads', 'logs', 'assets', 'templates'];
foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ $dir is writable<br>";
        } else {
            echo "⚠️ $dir exists but not writable<br>";
        }
    } else {
        echo "❌ $dir directory missing<br>";
    }
}

// Test 6: Session test
echo "<h2>6. Session Test</h2>";
try {
    session_start();
    echo "✅ Session started successfully<br>";
} catch (Exception $e) {
    echo "❌ Session error: " . $e->getMessage() . "<br>";
}

echo "<h2>7. Server Information</h2>";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "<br>";
echo "Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "<br>";

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>If database connection failed, check your database credentials in config/database.php</li>";
echo "<li>If database doesn't exist, import the database.sql file</li>";
echo "<li>If directories are missing, create them with proper permissions</li>";
echo "<li>If functions are missing, check the includes/functions.php file</li>";
echo "</ol>";
?>
