# Arabic Blog System - نظام المدونة العربية

A fully responsive, RTL Arabic blog system built with vanilla PHP and Tailwind CSS, featuring a complete admin dashboard and modern design.

## Features - المميزات

### Frontend - الواجهة الأمامية
- ✅ Fully responsive RTL design - تصميم متجاوب بالكامل يدعم RTL
- ✅ IBM Plex Sans Arabic font integration - دمج خط IBM Plex Sans Arabic
- ✅ Modern Tailwind CSS styling - تصميم عصري باستخدام Tailwind CSS
- ✅ Article listing with pagination - عرض المقالات مع ترقيم الصفحات
- ✅ Article detail pages with sharing buttons - صفحات تفاصيل المقالات مع أزرار المشاركة
- ✅ Search functionality - وظيفة البحث
- ✅ Tag-based filtering - التصفية حسب الوسوم
- ✅ Contact form - نموذج الاتصال
- ✅ SEO-friendly URLs - روابط صديقة لمحركات البحث

### Admin Dashboard - لوحة التحكم
- ✅ Secure admin authentication - مصادقة آمنة للمدير
- ✅ Dashboard with statistics - لوحة تحكم مع الإحصائيات
- ✅ Article management (CRUD) - إدارة المقالات (إنشاء، قراءة، تحديث، حذف)
- ✅ Rich text editor (TinyMCE) - محرر نصوص غني
- ✅ Image upload functionality - وظيفة رفع الصور
- ✅ Bulk operations - العمليات المجمعة
- ✅ Article status management - إدارة حالة المقالات
- ✅ User-friendly Arabic interface - واجهة عربية سهلة الاستخدام

### Security Features - مميزات الأمان
- ✅ CSRF protection - حماية من هجمات CSRF
- ✅ Input sanitization - تنظيف المدخلات
- ✅ SQL injection prevention - منع حقن SQL
- ✅ XSS protection - حماية من XSS
- ✅ Rate limiting - تحديد معدل الطلبات
- ✅ Session security - أمان الجلسات
- ✅ Login attempt limiting - تحديد محاولات تسجيل الدخول
- ✅ Secure file uploads - رفع آمن للملفات

## Installation - التثبيت

### Requirements - المتطلبات
- PHP 7.4 or higher - PHP 7.4 أو أحدث
- MySQL 5.7 or higher - MySQL 5.7 أو أحدث
- Apache/Nginx web server - خادم ويب Apache/Nginx
- mod_rewrite enabled - تفعيل mod_rewrite

### Setup Steps - خطوات التثبيت

1. **Clone or download the project - استنساخ أو تحميل المشروع**
   ```bash
   git clone [repository-url]
   cd arabic-blog
   ```

2. **Database Setup - إعداد قاعدة البيانات**
   - Create a MySQL database - إنشاء قاعدة بيانات MySQL
   - Import `database.sql` file - استيراد ملف database.sql
   ```sql
   mysql -u username -p database_name < database.sql
   ```

3. **Configuration - التكوين**
   - Edit `config/database.php` with your database credentials
   - تحرير ملف config/database.php بمعلومات قاعدة البيانات الخاصة بك
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'your_database_name');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

4. **Permissions - الصلاحيات**
   ```bash
   chmod 755 uploads/
   chmod 755 logs/
   ```

5. **Web Server Configuration - تكوين خادم الويب**
   - Ensure `.htaccess` is working (Apache) - تأكد من عمل .htaccess
   - Configure virtual host if needed - تكوين virtual host إذا لزم الأمر

## Default Login - تسجيل الدخول الافتراضي

- **URL:** `/admin/login.php`
- **Username:** `admin`
- **Password:** `admin123`

**⚠️ Important:** Change the default password immediately after first login!
**⚠️ مهم:** غيّر كلمة المرور الافتراضية فوراً بعد أول تسجيل دخول!

## File Structure - هيكل الملفات

```
arabic-blog/
├── admin/                  # Admin dashboard - لوحة التحكم
│   ├── includes/          # Admin includes - ملفات الإدراج للإدارة
│   ├── login.php          # Admin login - تسجيل دخول المدير
│   ├── dashboard.php      # Main dashboard - لوحة التحكم الرئيسية
│   ├── articles.php       # Article management - إدارة المقالات
│   └── add-article.php    # Add new article - إضافة مقال جديد
├── assets/                # Static assets - الملفات الثابتة
│   ├── css/              # Stylesheets - ملفات التنسيق
│   ├── js/               # JavaScript files - ملفات JavaScript
│   └── images/           # Images - الصور
├── config/               # Configuration files - ملفات التكوين
│   └── database.php      # Database config - تكوين قاعدة البيانات
├── includes/             # PHP includes - ملفات PHP المدرجة
│   ├── functions.php     # Core functions - الوظائف الأساسية
│   └── security.php      # Security functions - وظائف الأمان
├── templates/            # Template files - ملفات القوالب
│   ├── navigation.php    # Navigation bar - شريط التنقل
│   └── footer.php        # Footer - التذييل
├── uploads/              # Uploaded files - الملفات المرفوعة
├── logs/                 # Log files - ملفات السجلات
├── index.php             # Homepage - الصفحة الرئيسية
├── article.php           # Article detail page - صفحة تفاصيل المقال
├── search.php            # Search page - صفحة البحث
├── contact.php           # Contact page - صفحة الاتصال
├── database.sql          # Database schema - مخطط قاعدة البيانات
├── .htaccess            # Apache configuration - تكوين Apache
└── README.md            # This file - هذا الملف
```

## Usage - الاستخدام

### Admin Panel - لوحة التحكم
1. Access `/admin/login.php` - الوصول إلى صفحة تسجيل الدخول
2. Login with admin credentials - تسجيل الدخول بمعلومات المدير
3. Use the dashboard to manage articles - استخدام لوحة التحكم لإدارة المقالات

### Creating Articles - إنشاء المقالات
1. Go to "Add Article" - الذهاب إلى "إضافة مقال"
2. Fill in the article details - ملء تفاصيل المقال
3. Upload featured image (optional) - رفع صورة مميزة (اختياري)
4. Add tags separated by commas - إضافة وسوم مفصولة بفواصل
5. Choose to save as draft or publish - اختيار الحفظ كمسودة أو النشر

### Managing Articles - إدارة المقالات
- View all articles in the articles list - عرض جميع المقالات في قائمة المقالات
- Edit existing articles - تحرير المقالات الموجودة
- Change article status (draft/published) - تغيير حالة المقال (مسودة/منشور)
- Delete articles - حذف المقالات
- Bulk operations for multiple articles - عمليات مجمعة لعدة مقالات

## Customization - التخصيص

### Styling - التنسيق
- Edit `assets/css/style.css` for custom styles - تحرير ملف التنسيق للتخصيص
- Modify Tailwind configuration in HTML files - تعديل تكوين Tailwind في ملفات HTML

### Functionality - الوظائف
- Add new functions to `includes/functions.php` - إضافة وظائف جديدة
- Extend admin functionality in `admin/` directory - توسيع وظائف الإدارة

### Database - قاعدة البيانات
- Modify `database.sql` for schema changes - تعديل مخطط قاعدة البيانات
- Update functions accordingly - تحديث الوظائف وفقاً لذلك

## Security Considerations - اعتبارات الأمان

1. **Change default credentials** - غيّر المعلومات الافتراضية
2. **Use HTTPS in production** - استخدم HTTPS في الإنتاج
3. **Regular backups** - نسخ احتياطية منتظمة
4. **Keep PHP updated** - حافظ على تحديث PHP
5. **Monitor log files** - راقب ملفات السجلات
6. **Restrict file permissions** - قيّد صلاحيات الملفات

## Troubleshooting - استكشاف الأخطاء

### Common Issues - المشاكل الشائعة

1. **Database connection error** - خطأ اتصال قاعدة البيانات
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running

2. **Images not uploading** - الصور لا ترفع
   - Check `uploads/` directory permissions (755)
   - Verify PHP upload settings

3. **Admin login not working** - تسجيل دخول المدير لا يعمل
   - Verify database connection
   - Check if admin user exists in database

4. **Arabic text not displaying correctly** - النص العربي لا يظهر بشكل صحيح
   - Ensure database charset is utf8mb4
   - Check HTML meta charset

## Support - الدعم

For support and questions, please:
للدعم والأسئلة، يرجى:

- Check the documentation - مراجعة التوثيق
- Review the code comments - مراجعة تعليقات الكود
- Test with sample data - الاختبار ببيانات تجريبية

## License - الترخيص

This project is open source and available under the MIT License.
هذا المشروع مفتوح المصدر ومتاح تحت ترخيص MIT.

---

**Built with ❤️ for the Arabic developer community**
**مبني بـ ❤️ لمجتمع المطورين العرب**
