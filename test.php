<?php
/**
 * Simple test script to verify system functionality
 * Remove this file in production!
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Test - اختبار النظام</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-arabic bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">اختبار النظام - System Test</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            
            <!-- Database Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">اختبار قاعدة البيانات</h2>
                <?php
                try {
                    $testQuery = "SELECT COUNT(*) as count FROM articles";
                    $result = fetchRow($testQuery);
                    echo '<p class="text-green-600">✅ اتصال قاعدة البيانات يعمل</p>';
                    echo '<p class="text-gray-600">عدد المقالات: ' . $result['count'] . '</p>';
                } catch (Exception $e) {
                    echo '<p class="text-red-600">❌ خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>';
                }
                ?>
            </div>
            
            <!-- Functions Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">اختبار الوظائف</h2>
                <?php
                try {
                    $articles = getPublishedArticles(0, 3);
                    echo '<p class="text-green-600">✅ وظائف المقالات تعمل</p>';
                    echo '<p class="text-gray-600">تم جلب ' . count($articles) . ' مقالات</p>';
                } catch (Exception $e) {
                    echo '<p class="text-red-600">❌ خطأ في الوظائف: ' . $e->getMessage() . '</p>';
                }
                ?>
            </div>
            
            <!-- File Permissions Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">اختبار صلاحيات الملفات</h2>
                <?php
                $directories = ['uploads', 'logs'];
                foreach ($directories as $dir) {
                    if (is_dir($dir) && is_writable($dir)) {
                        echo '<p class="text-green-600">✅ مجلد ' . $dir . ' قابل للكتابة</p>';
                    } else {
                        echo '<p class="text-red-600">❌ مجلد ' . $dir . ' غير قابل للكتابة</p>';
                    }
                }
                ?>
            </div>
            
            <!-- PHP Configuration -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">تكوين PHP</h2>
                <p class="text-gray-600">إصدار PHP: <?php echo PHP_VERSION; ?></p>
                <p class="text-gray-600">حد رفع الملفات: <?php echo ini_get('upload_max_filesize'); ?></p>
                <p class="text-gray-600">حد POST: <?php echo ini_get('post_max_size'); ?></p>
                <p class="text-gray-600">المنطقة الزمنية: <?php echo date_default_timezone_get(); ?></p>
            </div>
            
            <!-- Security Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">اختبار الأمان</h2>
                <?php
                try {
                    $token = generateCSRFToken();
                    if ($token && strlen($token) > 10) {
                        echo '<p class="text-green-600">✅ CSRF tokens تعمل</p>';
                    } else {
                        echo '<p class="text-red-600">❌ مشكلة في CSRF tokens</p>';
                    }
                    
                    $testInput = '<script>alert("test")</script>';
                    $sanitized = sanitizeInput($testInput);
                    if ($sanitized !== $testInput) {
                        echo '<p class="text-green-600">✅ تنظيف المدخلات يعمل</p>';
                    } else {
                        echo '<p class="text-red-600">❌ مشكلة في تنظيف المدخلات</p>';
                    }
                } catch (Exception $e) {
                    echo '<p class="text-red-600">❌ خطأ في الأمان: ' . $e->getMessage() . '</p>';
                }
                ?>
            </div>
            
            <!-- Settings Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">اختبار الإعدادات</h2>
                <?php
                try {
                    $siteName = getSiteSetting('site_name', 'Default');
                    echo '<p class="text-green-600">✅ إعدادات الموقع تعمل</p>';
                    echo '<p class="text-gray-600">اسم الموقع: ' . htmlspecialchars($siteName) . '</p>';
                } catch (Exception $e) {
                    echo '<p class="text-red-600">❌ خطأ في الإعدادات: ' . $e->getMessage() . '</p>';
                }
                ?>
            </div>
        </div>
        
        <!-- Sample Articles -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold mb-4">المقالات المنشورة</h2>
            <?php
            try {
                $articles = getPublishedArticles(0, 5);
                if (empty($articles)) {
                    echo '<p class="text-yellow-600">⚠️ لا توجد مقالات منشورة</p>';
                } else {
                    echo '<div class="space-y-4">';
                    foreach ($articles as $article) {
                        echo '<div class="border-b border-gray-200 pb-4">';
                        echo '<h3 class="font-semibold">' . htmlspecialchars($article['title']) . '</h3>';
                        echo '<p class="text-gray-600 text-sm">تاريخ النشر: ' . formatArabicDate($article['created_at']) . '</p>';
                        echo '<p class="text-gray-600 text-sm">المشاهدات: ' . number_format($article['views']) . '</p>';
                        if ($article['tags']) {
                            echo '<p class="text-gray-600 text-sm">الوسوم: ' . htmlspecialchars($article['tags']) . '</p>';
                        }
                        echo '</div>';
                    }
                    echo '</div>';
                }
            } catch (Exception $e) {
                echo '<p class="text-red-600">❌ خطأ في جلب المقالات: ' . $e->getMessage() . '</p>';
            }
            ?>
        </div>
        
        <!-- Navigation Links -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold mb-4">روابط التنقل</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="index.php" class="bg-blue-600 text-white px-4 py-2 rounded text-center hover:bg-blue-700">
                    الصفحة الرئيسية
                </a>
                <a href="admin/login.php" class="bg-green-600 text-white px-4 py-2 rounded text-center hover:bg-green-700">
                    لوحة التحكم
                </a>
                <a href="search.php" class="bg-purple-600 text-white px-4 py-2 rounded text-center hover:bg-purple-700">
                    البحث
                </a>
                <a href="contact.php" class="bg-orange-600 text-white px-4 py-2 rounded text-center hover:bg-orange-700">
                    اتصل بنا
                </a>
            </div>
        </div>
        
        <!-- System Information -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold mb-4">معلومات النظام</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?>
                </div>
                <div>
                    <strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?>
                </div>
                <div>
                    <strong>MySQL:</strong> 
                    <?php 
                    try {
                        $version = fetchRow("SELECT VERSION() as version");
                        echo $version['version'];
                    } catch (Exception $e) {
                        echo 'غير متاح';
                    }
                    ?>
                </div>
                <div>
                    <strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?>
                </div>
            </div>
        </div>
        
        <div class="mt-8 text-center">
            <p class="text-red-600 font-bold">⚠️ احذف هذا الملف (test.php) في الإنتاج!</p>
            <p class="text-gray-600 mt-2">Delete this file (test.php) in production!</p>
        </div>
    </div>
</body>
</html>
