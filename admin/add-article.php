<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

$currentAdmin = getCurrentAdmin();
$errors = [];
$success = '';

// Handle form submission
if ($_POST) {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'رمز الأمان غير صحيح';
    } else {
        // Validate input
        $title = sanitizeInput($_POST['title'] ?? '');
        $content = $_POST['content'] ?? '';
        $excerpt = sanitizeInput($_POST['excerpt'] ?? '');
        $tags = sanitizeInput($_POST['tags'] ?? '');
        $status = $_POST['status'] ?? 'draft';
        
        // Validation
        if (empty($title)) {
            $errors[] = 'عنوان المقال مطلوب';
        }
        
        if (empty($content)) {
            $errors[] = 'محتوى المقال مطلوب';
        }
        
        if (!in_array($status, ['draft', 'published'])) {
            $errors[] = 'حالة المقال غير صحيحة';
        }
        
        // Handle image upload
        $imageName = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadImage($_FILES['image']);
            if (isset($uploadResult['error'])) {
                $errors[] = $uploadResult['error'];
            } else {
                $imageName = $uploadResult['filename'];
            }
        }
        
        // If no errors, create article
        if (empty($errors)) {
            $articleData = [
                'title' => $title,
                'content' => $content,
                'excerpt' => $excerpt ?: generateExcerpt($content),
                'image' => $imageName,
                'tags' => $tags,
                'status' => $status,
                'author_id' => $currentAdmin['id']
            ];
            
            $articleId = createArticle($articleData);
            
            if ($articleId) {
                logAdminActivity('create_article', "Created article: {$title}");
                $message = $status === 'published' ? 'تم إنشاء ونشر المقال بنجاح' : 'تم إنشاء المقال كمسودة بنجاح';
                redirectWithMessage('articles.php', $message, 'success');
            } else {
                $errors[] = 'فشل في إنشاء المقال';
            }
        }
    }
}

// Get flash message
$flashMessage = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مقال جديد - لوحة التحكم</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- IBM Plex Sans Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- TinyMCE Editor -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-arabic bg-gray-100">
    
    <!-- Admin Navigation -->
    <?php include 'includes/admin-nav.php'; ?>
    
    <!-- Main Content -->
    <div class="flex">
        <!-- Sidebar -->
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <!-- Content Area -->
        <main class="flex-1 p-8">
            
            <!-- Header -->
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">إضافة مقال جديد</h1>
                    <p class="text-gray-600">أنشئ مقالاً جديداً للمدونة</p>
                </div>
                <a href="articles.php" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition duration-300 flex items-center">
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m0 0h11a2 2 0 012 2v10a2 2 0 01-2 2H3a2 2 0 01-2-2V5a2 2 0 012-2z"></path>
                    </svg>
                    العودة للمقالات
                </a>
            </div>
            
            <!-- Flash Message -->
            <?php if ($flashMessage): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $flashMessage['type'] === 'success' ? 'bg-green-50 border border-green-200 text-green-700' : 'bg-red-50 border border-red-200 text-red-700'; ?>">
                    <?php echo htmlspecialchars($flashMessage['text']); ?>
                </div>
            <?php endif; ?>
            
            <!-- Errors -->
            <?php if (!empty($errors)): ?>
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h3 class="text-red-800 font-medium mb-2">يرجى تصحيح الأخطاء التالية:</h3>
                    <ul class="text-red-700 list-disc list-inside space-y-1">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <!-- Article Form -->
            <form method="POST" enctype="multipart/form-data" class="space-y-8">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    
                    <!-- Main Content -->
                    <div class="lg:col-span-2 space-y-6">
                        
                        <!-- Title -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                عنوان المقال *
                            </label>
                            <input type="text" 
                                   id="title" 
                                   name="title" 
                                   value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>"
                                   required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right text-lg"
                                   placeholder="أدخل عنوان المقال...">
                        </div>
                        
                        <!-- Content -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                                محتوى المقال *
                            </label>
                            <textarea id="content" 
                                      name="content" 
                                      rows="20" 
                                      required 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                                      placeholder="اكتب محتوى المقال هنا..."><?php echo htmlspecialchars($_POST['content'] ?? ''); ?></textarea>
                        </div>
                        
                        <!-- Excerpt -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-2">
                                المقتطف (اختياري)
                            </label>
                            <textarea id="excerpt" 
                                      name="excerpt" 
                                      rows="3" 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                                      placeholder="مقتطف قصير عن المقال (سيتم إنشاؤه تلقائياً إذا تُرك فارغاً)..."><?php echo htmlspecialchars($_POST['excerpt'] ?? ''); ?></textarea>
                            <p class="text-sm text-gray-500 mt-2">إذا تُرك فارغاً، سيتم إنشاء المقتطف تلقائياً من بداية المحتوى</p>
                        </div>
                    </div>
                    
                    <!-- Sidebar -->
                    <div class="space-y-6">
                        
                        <!-- Publish Options -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">خيارات النشر</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">حالة المقال</label>
                                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="draft" <?php echo ($_POST['status'] ?? 'draft') === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                                        <option value="published" <?php echo ($_POST['status'] ?? '') === 'published' ? 'selected' : ''; ?>>منشور</option>
                                    </select>
                                </div>
                                
                                <div class="flex space-x-3 space-x-reverse">
                                    <button type="submit" name="status" value="draft" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition duration-300">
                                        حفظ كمسودة
                                    </button>
                                    <button type="submit" name="status" value="published" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-300">
                                        نشر المقال
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Featured Image -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">الصورة المميزة</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <input type="file" 
                                           id="image" 
                                           name="image" 
                                           accept="image/*" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <p class="text-sm text-gray-500 mt-2">الحد الأقصى: 5 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF, WebP</p>
                                </div>
                                
                                <div id="image-preview" class="hidden">
                                    <img id="preview-img" src="" alt="معاينة الصورة" class="w-full rounded-lg">
                                    <button type="button" id="remove-image" class="mt-2 text-red-600 hover:text-red-800 text-sm">إزالة الصورة</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Tags -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">الوسوم</h3>
                            
                            <div>
                                <input type="text" 
                                       id="tags" 
                                       name="tags" 
                                       value="<?php echo htmlspecialchars($_POST['tags'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                                       placeholder="أدخل الوسوم مفصولة بفواصل...">
                                <p class="text-sm text-gray-500 mt-2">مثال: تقنية, برمجة, ويب</p>
                            </div>
                        </div>
                        
                        <!-- Article Info -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات المقال</h3>
                            
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">الكاتب:</span>
                                    <span class="font-medium"><?php echo htmlspecialchars($currentAdmin['full_name']); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">تاريخ الإنشاء:</span>
                                    <span class="font-medium"><?php echo formatArabicDate(date('Y-m-d H:i:s')); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </main>
    </div>
    
    <script>
        // Initialize TinyMCE
        tinymce.init({
            selector: '#content',
            height: 500,
            directionality: 'rtl',
            language: 'ar',
            plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
            toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
            content_style: 'body { font-family: "IBM Plex Sans Arabic", sans-serif; font-size: 16px; line-height: 1.6; }',
            setup: function (editor) {
                editor.on('change', function () {
                    editor.save();
                });
            }
        });
        
        // Image preview functionality
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview-img').src = e.target.result;
                    document.getElementById('image-preview').classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Remove image functionality
        document.getElementById('remove-image').addEventListener('click', function() {
            document.getElementById('image').value = '';
            document.getElementById('image-preview').classList.add('hidden');
        });
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const content = tinymce.get('content').getContent();
            
            if (!title) {
                e.preventDefault();
                alert('يرجى إدخال عنوان المقال');
                document.getElementById('title').focus();
                return;
            }
            
            if (!content || content.trim() === '') {
                e.preventDefault();
                alert('يرجى إدخال محتوى المقال');
                tinymce.get('content').focus();
                return;
            }
        });
        
        // Auto-save functionality (optional)
        let autoSaveTimer;
        function autoSave() {
            // Implementation for auto-save functionality
            console.log('Auto-saving...');
        }
        
        // Set up auto-save every 30 seconds
        document.addEventListener('DOMContentLoaded', function() {
            autoSaveTimer = setInterval(autoSave, 30000);
        });
    </script>
</body>
</html>
