<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin
$currentAdmin = getCurrentAdmin();

// Handle bulk actions
if ($_POST && isset($_POST['bulk_action']) && isset($_POST['selected_articles'])) {
    $action = $_POST['bulk_action'];
    $selectedArticles = $_POST['selected_articles'];
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('articles.php', 'رمز الأمان غير صحيح', 'error');
    }
    
    $successCount = 0;
    foreach ($selectedArticles as $articleId) {
        switch ($action) {
            case 'publish':
                if (updateArticle($articleId, ['status' => 'published'])) {
                    $successCount++;
                }
                break;
            case 'draft':
                if (updateArticle($articleId, ['status' => 'draft'])) {
                    $successCount++;
                }
                break;
            case 'delete':
                if (deleteArticle($articleId)) {
                    $successCount++;
                }
                break;
        }
    }
    
    $message = "تم تنفيذ العملية على {$successCount} مقال";
    redirectWithMessage('articles.php', $message, 'success');
}

// Handle single article actions
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $articleId = (int)$_GET['id'];
    
    if (!verifyCSRFToken($_GET['token'] ?? '')) {
        redirectWithMessage('articles.php', 'رمز الأمان غير صحيح', 'error');
    }
    
    switch ($action) {
        case 'delete':
            if (deleteArticle($articleId)) {
                redirectWithMessage('articles.php', 'تم حذف المقال بنجاح', 'success');
            } else {
                redirectWithMessage('articles.php', 'فشل في حذف المقال', 'error');
            }
            break;
        case 'publish':
            $article = getArticleById($articleId);
            if ($article && updateArticle($articleId, array_merge($article, ['status' => 'published']))) {
                redirectWithMessage('articles.php', 'تم نشر المقال بنجاح', 'success');
            } else {
                redirectWithMessage('articles.php', 'فشل في نشر المقال', 'error');
            }
            break;
        case 'draft':
            $article = getArticleById($articleId);
            if ($article && updateArticle($articleId, array_merge($article, ['status' => 'draft']))) {
                redirectWithMessage('articles.php', 'تم تحويل المقال إلى مسودة', 'success');
            } else {
                redirectWithMessage('articles.php', 'فشل في تحويل المقال إلى مسودة', 'error');
            }
            break;
    }
}

// Get filter parameters
$status = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Get articles
$articles = getAllArticles($offset, $perPage, $status);
$totalArticles = getTotalArticles($status);
$totalPages = ceil($totalArticles / $perPage);

// Get flash message
$flashMessage = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المقالات - لوحة التحكم</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- IBM Plex Sans Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-arabic bg-gray-100">
    
    <!-- Admin Navigation -->
    <?php include 'includes/admin-nav.php'; ?>
    
    <!-- Main Content -->
    <div class="flex">
        <!-- Sidebar -->
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <!-- Content Area -->
        <main class="flex-1 p-8">
            
            <!-- Header -->
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">إدارة المقالات</h1>
                    <p class="text-gray-600">عرض وإدارة جميع المقالات</p>
                </div>
                <a href="add-article.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300 flex items-center">
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة مقال جديد
                </a>
            </div>
            
            <!-- Flash Message -->
            <?php if ($flashMessage): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $flashMessage['type'] === 'success' ? 'bg-green-50 border border-green-200 text-green-700' : 'bg-red-50 border border-red-200 text-red-700'; ?>">
                    <?php echo htmlspecialchars($flashMessage['text']); ?>
                </div>
            <?php endif; ?>
            
            <!-- Filters -->
            <div class="bg-white rounded-lg shadow mb-6 p-6">
                <form method="GET" class="flex flex-wrap gap-4 items-end">
                    
                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select name="status" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">جميع المقالات</option>
                            <option value="published" <?php echo $status === 'published' ? 'selected' : ''; ?>>منشور</option>
                            <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                            <option value="archived" <?php echo $status === 'archived' ? 'selected' : ''; ?>>مؤرشف</option>
                        </select>
                    </div>
                    
                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                        <input type="text" 
                               name="search" 
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="البحث في العنوان أو المحتوى..."
                               class="border border-gray-300 rounded-lg px-3 py-2 w-64 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right">
                    </div>
                    
                    <!-- Submit -->
                    <div>
                        <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition duration-300">
                            تطبيق الفلتر
                        </button>
                        <a href="articles.php" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition duration-300 mr-2">
                            إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
            
            <!-- Articles Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                
                <?php if (empty($articles)): ?>
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مقالات</h3>
                        <p class="text-gray-500 mb-4">لم يتم العثور على أي مقالات تطابق المعايير المحددة</p>
                        <a href="add-article.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300">
                            إضافة مقال جديد
                        </a>
                    </div>
                <?php else: ?>
                    
                    <form method="POST" id="bulk-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <!-- Bulk Actions -->
                        <div class="p-4 border-b border-gray-200 bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4 space-x-reverse">
                                    <select name="bulk_action" class="border border-gray-300 rounded px-3 py-1 text-sm">
                                        <option value="">اختر عملية...</option>
                                        <option value="publish">نشر</option>
                                        <option value="draft">تحويل لمسودة</option>
                                        <option value="delete">حذف</option>
                                    </select>
                                    <button type="submit" class="bg-gray-600 text-white px-4 py-1 rounded text-sm hover:bg-gray-700 transition duration-300">
                                        تطبيق
                                    </button>
                                </div>
                                <div class="text-sm text-gray-600">
                                    إجمالي: <?php echo number_format($totalArticles); ?> مقال
                                </div>
                            </div>
                        </div>
                        
                        <!-- Table -->
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input type="checkbox" id="select-all" class="rounded">
                                        </th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العنوان</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكاتب</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المشاهدات</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($articles as $article): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="checkbox" name="selected_articles[]" value="<?php echo $article['id']; ?>" class="rounded article-checkbox">
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="flex items-center">
                                                    <?php if ($article['image']): ?>
                                                        <img src="../uploads/<?php echo htmlspecialchars($article['image']); ?>" 
                                                             alt="" 
                                                             class="w-10 h-10 rounded object-cover ml-3">
                                                    <?php endif; ?>
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900">
                                                            <?php echo htmlspecialchars($article['title']); ?>
                                                        </div>
                                                        <?php if ($article['tags']): ?>
                                                            <div class="text-xs text-gray-500 mt-1">
                                                                <?php echo htmlspecialchars($article['tags']); ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo htmlspecialchars($article['author_name']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 py-1 text-xs rounded-full <?php 
                                                    echo $article['status'] === 'published' ? 'bg-green-100 text-green-800' : 
                                                        ($article['status'] === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'); 
                                                ?>">
                                                    <?php 
                                                        echo $article['status'] === 'published' ? 'منشور' : 
                                                            ($article['status'] === 'draft' ? 'مسودة' : 'مؤرشف'); 
                                                    ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($article['views']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php echo formatArabicDate($article['created_at']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2 space-x-reverse">
                                                    <a href="edit-article.php?id=<?php echo $article['id']; ?>" 
                                                       class="text-blue-600 hover:text-blue-900">تحرير</a>
                                                    
                                                    <?php if ($article['status'] === 'published'): ?>
                                                        <a href="../article.php?id=<?php echo $article['id']; ?>" 
                                                           target="_blank" 
                                                           class="text-green-600 hover:text-green-900">عرض</a>
                                                        <a href="?action=draft&id=<?php echo $article['id']; ?>&token=<?php echo generateCSRFToken(); ?>" 
                                                           class="text-yellow-600 hover:text-yellow-900">إلغاء النشر</a>
                                                    <?php else: ?>
                                                        <a href="?action=publish&id=<?php echo $article['id']; ?>&token=<?php echo generateCSRFToken(); ?>" 
                                                           class="text-green-600 hover:text-green-900">نشر</a>
                                                    <?php endif; ?>
                                                    
                                                    <a href="?action=delete&id=<?php echo $article['id']; ?>&token=<?php echo generateCSRFToken(); ?>" 
                                                       class="text-red-600 hover:text-red-900"
                                                       onclick="return confirm('هل أنت متأكد من حذف هذا المقال؟')">حذف</a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                            <div class="flex justify-between items-center">
                                <div class="text-sm text-gray-700">
                                    عرض <?php echo (($page - 1) * $perPage) + 1; ?> إلى <?php echo min($page * $perPage, $totalArticles); ?> من <?php echo $totalArticles; ?> مقال
                                </div>
                                <div class="flex space-x-2 space-x-reverse">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?php echo $page - 1; ?>&status=<?php echo $status; ?>&search=<?php echo urlencode($search); ?>" 
                                           class="px-3 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50">السابق</a>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <a href="?page=<?php echo $i; ?>&status=<?php echo $status; ?>&search=<?php echo urlencode($search); ?>" 
                                           class="px-3 py-1 <?php echo $i == $page ? 'bg-blue-600 text-white' : 'bg-white border border-gray-300 hover:bg-gray-50'; ?> rounded">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <a href="?page=<?php echo $page + 1; ?>&status=<?php echo $status; ?>&search=<?php echo urlencode($search); ?>" 
                                           class="px-3 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50">التالي</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <script>
        // Select all functionality
        document.getElementById('select-all').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.article-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
        
        // Bulk form validation
        document.getElementById('bulk-form').addEventListener('submit', function(e) {
            const action = document.querySelector('select[name="bulk_action"]').value;
            const selected = document.querySelectorAll('.article-checkbox:checked');
            
            if (!action) {
                e.preventDefault();
                alert('يرجى اختيار عملية للتنفيذ');
                return;
            }
            
            if (selected.length === 0) {
                e.preventDefault();
                alert('يرجى اختيار مقال واحد على الأقل');
                return;
            }
            
            if (action === 'delete') {
                if (!confirm(`هل أنت متأكد من حذف ${selected.length} مقال؟`)) {
                    e.preventDefault();
                }
            }
        });
    </script>
</body>
</html>
