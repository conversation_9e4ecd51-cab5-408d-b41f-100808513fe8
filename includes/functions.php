<?php
/**
 * Core Functions for Arabic Blog System
 * Contains all helper functions for articles, authentication, formatting, and utilities
 */

// Prevent direct access
if (!defined('DB_HOST')) {
    die('Direct access not allowed');
}

// Include security functions
require_once __DIR__ . '/security.php';

/**
 * ARTICLE FUNCTIONS
 */

/**
 * Get published articles with pagination
 */
function getPublishedArticles($offset = 0, $limit = 6) {
    $sql = "SELECT a.*, u.full_name as author_name 
            FROM articles a 
            LEFT JOIN admin_users u ON a.author_id = u.id 
            WHERE a.status = 'published' 
            ORDER BY a.published_at DESC, a.created_at DESC 
            LIMIT :limit OFFSET :offset";
    
    return fetchAll($sql, [
        ':limit' => $limit,
        ':offset' => $offset
    ]);
}

/**
 * Get total count of published articles
 */
function getTotalPublishedArticles() {
    $sql = "SELECT COUNT(*) as total FROM articles WHERE status = 'published'";
    $result = fetchRow($sql);
    return $result ? $result['total'] : 0;
}

/**
 * Get article by ID
 */
function getArticleById($id) {
    $sql = "SELECT a.*, u.full_name as author_name 
            FROM articles a 
            LEFT JOIN admin_users u ON a.author_id = u.id 
            WHERE a.id = :id";
    
    return fetchRow($sql, [':id' => $id]);
}

/**
 * Get published article by ID and increment views
 */
function getPublishedArticleById($id) {
    $article = fetchRow(
        "SELECT a.*, u.full_name as author_name 
         FROM articles a 
         LEFT JOIN admin_users u ON a.author_id = u.id 
         WHERE a.id = :id AND a.status = 'published'",
        [':id' => $id]
    );
    
    if ($article) {
        // Increment view count
        executeQuery("UPDATE articles SET views = views + 1 WHERE id = :id", [':id' => $id]);
    }
    
    return $article;
}

/**
 * Get all articles for admin (with pagination)
 */
function getAllArticles($offset = 0, $limit = 10, $status = null) {
    $sql = "SELECT a.*, u.full_name as author_name 
            FROM articles a 
            LEFT JOIN admin_users u ON a.author_id = u.id";
    
    $params = [];
    
    if ($status) {
        $sql .= " WHERE a.status = :status";
        $params[':status'] = $status;
    }
    
    $sql .= " ORDER BY a.created_at DESC LIMIT :limit OFFSET :offset";
    $params[':limit'] = $limit;
    $params[':offset'] = $offset;
    
    return fetchAll($sql, $params);
}

/**
 * Get total articles count for admin
 */
function getTotalArticles($status = null) {
    $sql = "SELECT COUNT(*) as total FROM articles";
    $params = [];
    
    if ($status) {
        $sql .= " WHERE status = :status";
        $params[':status'] = $status;
    }
    
    $result = fetchRow($sql, $params);
    return $result ? $result['total'] : 0;
}

/**
 * Create new article
 */
function createArticle($data) {
    $sql = "INSERT INTO articles (title, content, excerpt, image, tags, status, author_id, published_at) 
            VALUES (:title, :content, :excerpt, :image, :tags, :status, :author_id, :published_at)";
    
    $published_at = ($data['status'] === 'published') ? date('Y-m-d H:i:s') : null;
    
    $params = [
        ':title' => $data['title'],
        ':content' => $data['content'],
        ':excerpt' => $data['excerpt'] ?? generateExcerpt($data['content']),
        ':image' => $data['image'] ?? null,
        ':tags' => $data['tags'] ?? null,
        ':status' => $data['status'],
        ':author_id' => $data['author_id'],
        ':published_at' => $published_at
    ];
    
    $result = executeQuery($sql, $params);
    return $result ? getLastInsertId() : false;
}

/**
 * Update article
 */
function updateArticle($id, $data) {
    $sql = "UPDATE articles SET 
            title = :title, 
            content = :content, 
            excerpt = :excerpt, 
            image = :image, 
            tags = :tags, 
            status = :status,
            updated_at = CURRENT_TIMESTAMP";
    
    $params = [
        ':title' => $data['title'],
        ':content' => $data['content'],
        ':excerpt' => $data['excerpt'] ?? generateExcerpt($data['content']),
        ':image' => $data['image'] ?? null,
        ':tags' => $data['tags'] ?? null,
        ':status' => $data['status'],
        ':id' => $id
    ];
    
    // Update published_at if status changed to published
    if ($data['status'] === 'published') {
        $current = getArticleById($id);
        if ($current && $current['status'] !== 'published') {
            $sql .= ", published_at = CURRENT_TIMESTAMP";
        }
    }
    
    $sql .= " WHERE id = :id";
    
    return executeQuery($sql, $params) !== false;
}

/**
 * Delete article
 */
function deleteArticle($id) {
    // Get article to delete associated image
    $article = getArticleById($id);
    if ($article && $article['image']) {
        $imagePath = 'uploads/' . $article['image'];
        if (file_exists($imagePath)) {
            unlink($imagePath);
        }
    }
    
    $sql = "DELETE FROM articles WHERE id = :id";
    return executeQuery($sql, [':id' => $id]) !== false;
}

/**
 * Search articles
 */
function searchArticles($query, $offset = 0, $limit = 6) {
    $sql = "SELECT a.*, u.full_name as author_name 
            FROM articles a 
            LEFT JOIN admin_users u ON a.author_id = u.id 
            WHERE a.status = 'published' 
            AND (a.title LIKE :query OR a.content LIKE :query OR a.tags LIKE :query)
            ORDER BY a.published_at DESC 
            LIMIT :limit OFFSET :offset";
    
    $searchTerm = '%' . $query . '%';
    return fetchAll($sql, [
        ':query' => $searchTerm,
        ':limit' => $limit,
        ':offset' => $offset
    ]);
}

/**
 * Get articles by tag
 */
function getArticlesByTag($tag, $offset = 0, $limit = 6) {
    $sql = "SELECT a.*, u.full_name as author_name 
            FROM articles a 
            LEFT JOIN admin_users u ON a.author_id = u.id 
            WHERE a.status = 'published' 
            AND a.tags LIKE :tag
            ORDER BY a.published_at DESC 
            LIMIT :limit OFFSET :offset";
    
    return fetchAll($sql, [
        ':tag' => '%' . $tag . '%',
        ':limit' => $limit,
        ':offset' => $offset
    ]);
}

/**
 * Get related articles (by tags)
 */
function getRelatedArticles($articleId, $tags, $limit = 3) {
    if (empty($tags)) return [];
    
    $tagArray = array_map('trim', explode(',', $tags));
    $tagConditions = [];
    $params = [':article_id' => $articleId, ':limit' => $limit];
    
    foreach ($tagArray as $index => $tag) {
        $tagConditions[] = "a.tags LIKE :tag{$index}";
        $params[":tag{$index}"] = '%' . $tag . '%';
    }
    
    $sql = "SELECT a.*, u.full_name as author_name 
            FROM articles a 
            LEFT JOIN admin_users u ON a.author_id = u.id 
            WHERE a.status = 'published' 
            AND a.id != :article_id
            AND (" . implode(' OR ', $tagConditions) . ")
            ORDER BY a.published_at DESC 
            LIMIT :limit";
    
    return fetchAll($sql, $params);
}

/**
 * AUTHENTICATION FUNCTIONS
 */

/**
 * Authenticate admin user
 */
function authenticateAdmin($username, $password) {
    try {
        // Check login attempts
        Security::checkLoginAttempts($username);

        $sql = "SELECT * FROM admin_users WHERE username = :username AND is_active = 1";
        $user = fetchRow($sql, [':username' => $username]);

        if ($user && password_verify($password, $user['password'])) {
            // Clear failed login attempts
            Security::clearLoginAttempts($username);

            // Update last login
            executeQuery(
                "UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = :id",
                [':id' => $user['id']]
            );

            // Regenerate session ID for security
            session_regenerate_id(true);

            // Set session
            $_SESSION['admin_id'] = $user['id'];
            $_SESSION['admin_username'] = $user['username'];
            $_SESSION['admin_name'] = $user['full_name'];
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['login_time'] = time();

            return true;
        } else {
            // Record failed login attempt
            Security::recordFailedLogin($username);
            return false;
        }
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * Check if admin is logged in
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

/**
 * Get current admin user
 */
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return null;
    }

    $sql = "SELECT * FROM admin_users WHERE id = :id";
    return fetchRow($sql, [':id' => $_SESSION['admin_id']]);
}

/**
 * Logout admin
 */
function logoutAdmin() {
    session_unset();
    session_destroy();
}

/**
 * Require admin authentication
 */
function requireAdmin() {
    if (!isAdminLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

/**
 * UTILITY FUNCTIONS
 */

/**
 * Generate excerpt from content
 */
function generateExcerpt($content, $length = 150) {
    $text = strip_tags($content);
    if (mb_strlen($text, 'UTF-8') <= $length) {
        return $text;
    }
    return mb_substr($text, 0, $length, 'UTF-8') . '...';
}

/**
 * Get excerpt (alias for backward compatibility)
 */
function getExcerpt($content, $length = 150) {
    return generateExcerpt($content, $length);
}

/**
 * Format Arabic date
 */
function formatArabicDate($date, $format = 'full') {
    $timestamp = strtotime($date);

    $arabicMonths = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];

    $day = date('j', $timestamp);
    $month = $arabicMonths[(int)date('n', $timestamp)];
    $year = date('Y', $timestamp);

    if ($format === 'short') {
        return "{$day} {$month} {$year}";
    }

    return "{$day} {$month} {$year}";
}

/**
 * Sanitize input
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Upload image file
 */
function uploadImage($file, $uploadDir = 'uploads/') {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return false;
    }

    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    // Check file type
    if (!in_array($file['type'], $allowedTypes)) {
        return ['error' => 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF أو WebP'];
    }

    // Check file size
    if ($file['size'] > $maxSize) {
        return ['error' => 'حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت'];
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;

    // Create upload directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename];
    }

    return ['error' => 'فشل في رفع الملف'];
}

/**
 * Delete image file
 */
function deleteImage($filename, $uploadDir = 'uploads/') {
    $filepath = $uploadDir . $filename;
    if (file_exists($filepath)) {
        return unlink($filepath);
    }
    return false;
}

/**
 * Get site settings
 */
function getSiteSetting($key, $default = null) {
    $sql = "SELECT setting_value FROM settings WHERE setting_key = :key";
    $result = fetchRow($sql, [':key' => $key]);
    return $result ? $result['setting_value'] : $default;
}

/**
 * Update site setting
 */
function updateSiteSetting($key, $value) {
    $sql = "INSERT INTO settings (setting_key, setting_value)
            VALUES (:key, :value)
            ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = CURRENT_TIMESTAMP";

    return executeQuery($sql, [':key' => $key, ':value' => $value]) !== false;
}

/**
 * Get dashboard statistics
 */
function getDashboardStats() {
    $stats = [];

    // Total articles
    $stats['total_articles'] = getTotalArticles();

    // Published articles
    $stats['published_articles'] = getTotalArticles('published');

    // Draft articles
    $stats['draft_articles'] = getTotalArticles('draft');

    // Total views
    $result = fetchRow("SELECT SUM(views) as total_views FROM articles WHERE status = 'published'");
    $stats['total_views'] = $result ? (int)$result['total_views'] : 0;

    // Recent articles
    $stats['recent_articles'] = getAllArticles(0, 5);

    // Popular articles
    $stats['popular_articles'] = fetchAll(
        "SELECT title, views FROM articles WHERE status = 'published' ORDER BY views DESC LIMIT 5"
    );

    return $stats;
}

/**
 * Log admin activity
 */
function logAdminActivity($action, $details = null) {
    if (!isAdminLoggedIn()) return false;

    $sql = "INSERT INTO admin_activity_log (admin_id, action, details, ip_address, user_agent)
            VALUES (:admin_id, :action, :details, :ip_address, :user_agent)";

    return executeQuery($sql, [
        ':admin_id' => $_SESSION['admin_id'],
        ':action' => $action,
        ':details' => $details,
        ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
        ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
    ]);
}

/**
 * Get popular tags
 */
function getPopularTags($limit = 10) {
    $sql = "SELECT tags FROM articles WHERE status = 'published' AND tags IS NOT NULL AND tags != ''";
    $articles = fetchAll($sql);

    $tagCounts = [];
    foreach ($articles as $article) {
        $tags = array_map('trim', explode(',', $article['tags']));
        foreach ($tags as $tag) {
            if (!empty($tag)) {
                $tagCounts[$tag] = ($tagCounts[$tag] ?? 0) + 1;
            }
        }
    }

    arsort($tagCounts);
    return array_slice($tagCounts, 0, $limit, true);
}

/**
 * Clean old sessions (call periodically)
 */
function cleanOldSessions() {
    // This would typically clean a sessions table if you're storing sessions in DB
    // For file-based sessions, PHP handles this automatically
    return true;
}

/**
 * Send email (basic implementation)
 */
function sendEmail($to, $subject, $message, $headers = null) {
    if (!$headers) {
        $headers = "From: " . getSiteSetting('site_email', '<EMAIL>') . "\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    }

    return mail($to, $subject, $message, $headers);
}

/**
 * Get client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];

    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
    }

    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header("Location: $url");
    exit;
}

/**
 * Get and clear flash message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = [
            'text' => $_SESSION['flash_message'],
            'type' => $_SESSION['flash_type'] ?? 'info'
        ];
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return $message;
    }
    return null;
}

/**
 * Get current URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];
    return $protocol . '://' . $host . $uri;
}

/**
 * Get base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . ($path === '/' ? '' : $path);
}

?>
