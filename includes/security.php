<?php
/**
 * Security Configuration and Functions
 * Additional security measures for the Arabic Blog system
 */

// Prevent direct access
if (!defined('DB_HOST')) {
    die('Direct access not allowed');
}

/**
 * Security Configuration
 */
class Security {
    
    // Maximum login attempts
    const MAX_LOGIN_ATTEMPTS = 5;
    
    // Lockout duration in minutes
    const LOCKOUT_DURATION = 30;
    
    // Session timeout in minutes
    const SESSION_TIMEOUT = 120;
    
    // Password requirements
    const MIN_PASSWORD_LENGTH = 8;
    
    /**
     * Initialize security measures
     */
    public static function init() {
        // Start secure session
        self::startSecureSession();
        
        // Set security headers
        self::setSecurityHeaders();
        
        // Check for suspicious activity
        self::checkSuspiciousActivity();
        
        // Clean old sessions
        self::cleanOldSessions();
    }
    
    /**
     * Start secure session
     */
    public static function startSecureSession() {
        // Configure session settings
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        
        // Regenerate session ID periodically
        if (isset($_SESSION['last_regeneration'])) {
            if (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
                session_regenerate_id(true);
                $_SESSION['last_regeneration'] = time();
            }
        } else {
            $_SESSION['last_regeneration'] = time();
        }
        
        // Check session timeout
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > (self::SESSION_TIMEOUT * 60)) {
                session_unset();
                session_destroy();
                session_start();
            }
        }
        $_SESSION['last_activity'] = time();
    }
    
    /**
     * Set security headers
     */
    public static function setSecurityHeaders() {
        if (!headers_sent()) {
            header('X-Frame-Options: SAMEORIGIN');
            header('X-Content-Type-Options: nosniff');
            header('X-XSS-Protection: 1; mode=block');
            header('Referrer-Policy: strict-origin-when-cross-origin');
            header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
        }
    }
    
    /**
     * Check for suspicious activity
     */
    public static function checkSuspiciousActivity() {
        $ip = getClientIP();
        
        // Check for too many requests
        $key = 'requests_' . md5($ip);
        $requests = $_SESSION[$key] ?? 0;
        
        if ($requests > 100) { // 100 requests per session
            self::logSuspiciousActivity('Too many requests', $ip);
            http_response_code(429);
            die('Too Many Requests');
        }
        
        $_SESSION[$key] = $requests + 1;
        
        // Check for SQL injection attempts
        $suspicious_patterns = [
            '/union.*select/i',
            '/drop.*table/i',
            '/insert.*into/i',
            '/delete.*from/i',
            '/update.*set/i',
            '/<script/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i'
        ];
        
        $request_data = array_merge($_GET, $_POST, $_COOKIE);
        foreach ($request_data as $key => $value) {
            if (is_string($value)) {
                foreach ($suspicious_patterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        self::logSuspiciousActivity("Suspicious pattern detected: {$pattern}", $ip, $value);
                        http_response_code(403);
                        die('Forbidden');
                    }
                }
            }
        }
    }
    
    /**
     * Log suspicious activity
     */
    public static function logSuspiciousActivity($activity, $ip, $data = null) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $ip,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'activity' => $activity,
            'data' => $data,
            'url' => $_SERVER['REQUEST_URI'] ?? ''
        ];
        
        $log_file = 'logs/security.log';
        if (!is_dir('logs')) {
            mkdir('logs', 0755, true);
        }
        
        file_put_contents($log_file, json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Check login attempts
     */
    public static function checkLoginAttempts($username) {
        $ip = getClientIP();
        $key = 'login_attempts_' . md5($ip . $username);
        
        $attempts = $_SESSION[$key] ?? ['count' => 0, 'last_attempt' => 0];
        
        // Reset attempts if lockout period has passed
        if (time() - $attempts['last_attempt'] > (self::LOCKOUT_DURATION * 60)) {
            $attempts = ['count' => 0, 'last_attempt' => 0];
        }
        
        if ($attempts['count'] >= self::MAX_LOGIN_ATTEMPTS) {
            $remaining = self::LOCKOUT_DURATION - ((time() - $attempts['last_attempt']) / 60);
            throw new Exception("تم تجاوز عدد محاولات تسجيل الدخول المسموح. يرجى المحاولة بعد " . ceil($remaining) . " دقيقة");
        }
        
        return $attempts;
    }
    
    /**
     * Record failed login attempt
     */
    public static function recordFailedLogin($username) {
        $ip = getClientIP();
        $key = 'login_attempts_' . md5($ip . $username);
        
        $attempts = $_SESSION[$key] ?? ['count' => 0, 'last_attempt' => 0];
        $attempts['count']++;
        $attempts['last_attempt'] = time();
        
        $_SESSION[$key] = $attempts;
        
        self::logSuspiciousActivity("Failed login attempt for user: {$username}", $ip);
    }
    
    /**
     * Clear login attempts on successful login
     */
    public static function clearLoginAttempts($username) {
        $ip = getClientIP();
        $key = 'login_attempts_' . md5($ip . $username);
        unset($_SESSION[$key]);
    }
    
    /**
     * Validate password strength
     */
    public static function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < self::MIN_PASSWORD_LENGTH) {
            $errors[] = "كلمة المرور يجب أن تكون " . self::MIN_PASSWORD_LENGTH . " أحرف على الأقل";
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل";
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل";
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل";
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل";
        }
        
        return $errors;
    }
    
    /**
     * Hash password securely
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3,         // 3 threads
        ]);
    }
    
    /**
     * Clean old sessions and temporary files
     */
    public static function cleanOldSessions() {
        // This should be called periodically (e.g., via cron job)
        if (rand(1, 100) === 1) { // 1% chance
            // Clean session files older than session timeout
            $session_path = session_save_path() ?: sys_get_temp_dir();
            $files = glob($session_path . '/sess_*');
            
            foreach ($files as $file) {
                if (filemtime($file) < time() - (self::SESSION_TIMEOUT * 60)) {
                    unlink($file);
                }
            }
            
            // Clean old log files (keep last 30 days)
            if (is_dir('logs')) {
                $log_files = glob('logs/*.log');
                foreach ($log_files as $file) {
                    if (filemtime($file) < time() - (30 * 24 * 60 * 60)) {
                        unlink($file);
                    }
                }
            }
        }
    }
    
    /**
     * Sanitize file upload
     */
    public static function sanitizeUpload($file) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $max_size = 5 * 1024 * 1024; // 5MB
        
        if (!in_array($file['type'], $allowed_types)) {
            throw new Exception('نوع الملف غير مدعوم');
        }
        
        if ($file['size'] > $max_size) {
            throw new Exception('حجم الملف كبير جداً');
        }
        
        // Check if it's actually an image
        $image_info = getimagesize($file['tmp_name']);
        if (!$image_info) {
            throw new Exception('الملف ليس صورة صحيحة');
        }
        
        // Generate safe filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $safe_name = uniqid() . '_' . time() . '.' . $extension;
        
        return $safe_name;
    }
    
    /**
     * Rate limiting
     */
    public static function rateLimit($action, $limit = 10, $window = 60) {
        $ip = getClientIP();
        $key = "rate_limit_{$action}_" . md5($ip);
        
        $attempts = $_SESSION[$key] ?? ['count' => 0, 'window_start' => time()];
        
        // Reset if window has passed
        if (time() - $attempts['window_start'] > $window) {
            $attempts = ['count' => 0, 'window_start' => time()];
        }
        
        if ($attempts['count'] >= $limit) {
            throw new Exception('تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً');
        }
        
        $attempts['count']++;
        $_SESSION[$key] = $attempts;
    }
}

// Initialize security on every request
Security::init();
?>
