<?php
/**
 * Setup Script for Arabic Blog System
 * This script helps set up the database and initial configuration
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

$step = $_GET['step'] ?? 1;
$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    switch ($_POST['action']) {
        case 'test_connection':
            $host = $_POST['host'];
            $user = $_POST['user'];
            $pass = $_POST['pass'];
            
            try {
                $pdo = new PDO("mysql:host=$host", $user, $pass);
                $message = "✅ اتصال قاعدة البيانات نجح!";
                $step = 2;
            } catch (PDOException $e) {
                $error = "❌ فشل الاتصال: " . $e->getMessage();
            }
            break;
            
        case 'create_database':
            $host = $_POST['host'];
            $user = $_POST['user'];
            $pass = $_POST['pass'];
            $dbname = $_POST['dbname'];
            
            try {
                // Connect without database
                $pdo = new PDO("mysql:host=$host", $user, $pass);
                
                // Create database
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                
                // Update config file
                $config = "<?php
/**
 * Database Configuration
 * Arabic Blog System
 */

// Database configuration
define('DB_HOST', '$host');
define('DB_NAME', '$dbname');
define('DB_USER', '$user');
define('DB_PASS', '$pass');
define('DB_CHARSET', 'utf8mb4');

// Create database connection
try {
    \$pdo = new PDO(
        \"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=\" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES \" . DB_CHARSET
        ]
    );
} catch (PDOException \$e) {
    die(\"Database connection failed: \" . \$e->getMessage());
}

/**
 * Execute a prepared statement with parameters
 */
function executeQuery(\$sql, \$params = []) {
    global \$pdo;
    try {
        \$stmt = \$pdo->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt;
    } catch (PDOException \$e) {
        error_log(\"Database query error: \" . \$e->getMessage());
        return false;
    }
}

/**
 * Get a single row from database
 */
function fetchRow(\$sql, \$params = []) {
    \$stmt = executeQuery(\$sql, \$params);
    return \$stmt ? \$stmt->fetch() : false;
}

/**
 * Get multiple rows from database
 */
function fetchAll(\$sql, \$params = []) {
    \$stmt = executeQuery(\$sql, \$params);
    return \$stmt ? \$stmt->fetchAll() : [];
}

/**
 * Get the last inserted ID
 */
function getLastInsertId() {
    global \$pdo;
    return \$pdo->lastInsertId();
}

/**
 * Begin transaction
 */
function beginTransaction() {
    global \$pdo;
    return \$pdo->beginTransaction();
}

/**
 * Commit transaction
 */
function commitTransaction() {
    global \$pdo;
    return \$pdo->commit();
}

/**
 * Rollback transaction
 */
function rollbackTransaction() {
    global \$pdo;
    return \$pdo->rollBack();
}
?>";
                
                file_put_contents('config/database.php', $config);
                $message = "✅ تم إنشاء قاعدة البيانات وتحديث ملف التكوين!";
                $step = 3;
                
            } catch (PDOException $e) {
                $error = "❌ فشل في إنشاء قاعدة البيانات: " . $e->getMessage();
            }
            break;
            
        case 'import_tables':
            try {
                require_once 'config/database.php';
                
                // Read SQL file
                $sql = file_get_contents('database.sql');
                
                // Execute SQL
                $pdo->exec($sql);
                
                $message = "✅ تم استيراد جداول قاعدة البيانات بنجاح!";
                $step = 4;
                
            } catch (Exception $e) {
                $error = "❌ فشل في استيراد الجداول: " . $e->getMessage();
            }
            break;
            
        case 'create_directories':
            $dirs = ['uploads', 'logs'];
            $created = [];
            
            foreach ($dirs as $dir) {
                if (!is_dir($dir)) {
                    if (mkdir($dir, 0755, true)) {
                        $created[] = $dir;
                    }
                } else {
                    $created[] = $dir . ' (موجود مسبقاً)';
                }
            }
            
            $message = "✅ تم إنشاء المجلدات: " . implode(', ', $created);
            $step = 5;
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام - مدونة عربية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-arabic bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-2xl mx-auto px-4">
            
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">إعداد نظام المدونة العربية</h1>
                <p class="text-gray-600">اتبع الخطوات التالية لإعداد النظام</p>
            </div>
            
            <!-- Progress -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center <?php echo $i <= $step ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'; ?>">
                                <?php echo $i; ?>
                            </div>
                            <?php if ($i < 5): ?>
                                <div class="w-12 h-1 <?php echo $i < $step ? 'bg-blue-600' : 'bg-gray-300'; ?>"></div>
                            <?php endif; ?>
                        </div>
                    <?php endfor; ?>
                </div>
                <div class="text-sm text-gray-600 text-center">
                    الخطوة <?php echo $step; ?> من 5
                </div>
            </div>
            
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <!-- Step Content -->
            <div class="bg-white rounded-lg shadow p-6">
                
                <?php if ($step == 1): ?>
                    <h2 class="text-xl font-bold mb-4">الخطوة 1: اختبار اتصال قاعدة البيانات</h2>
                    <form method="POST">
                        <input type="hidden" name="action" value="test_connection">
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">خادم قاعدة البيانات</label>
                                <input type="text" name="host" value="localhost" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                                <input type="text" name="user" value="root" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                                <input type="password" name="pass" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right">
                            </div>
                            
                            <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-300">
                                اختبار الاتصال
                            </button>
                        </div>
                    </form>
                    
                <?php elseif ($step == 2): ?>
                    <h2 class="text-xl font-bold mb-4">الخطوة 2: إنشاء قاعدة البيانات</h2>
                    <form method="POST">
                        <input type="hidden" name="action" value="create_database">
                        <input type="hidden" name="host" value="<?php echo $_POST['host'] ?? 'localhost'; ?>">
                        <input type="hidden" name="user" value="<?php echo $_POST['user'] ?? 'root'; ?>">
                        <input type="hidden" name="pass" value="<?php echo $_POST['pass'] ?? ''; ?>">
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم قاعدة البيانات</label>
                            <input type="text" name="dbname" value="arabic_blog" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right">
                        </div>
                        
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-300">
                            إنشاء قاعدة البيانات
                        </button>
                    </form>
                    
                <?php elseif ($step == 3): ?>
                    <h2 class="text-xl font-bold mb-4">الخطوة 3: استيراد جداول قاعدة البيانات</h2>
                    <p class="text-gray-600 mb-4">سيتم الآن استيراد الجداول والبيانات الأولية من ملف database.sql</p>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="import_tables">
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-300">
                            استيراد الجداول
                        </button>
                    </form>
                    
                <?php elseif ($step == 4): ?>
                    <h2 class="text-xl font-bold mb-4">الخطوة 4: إنشاء المجلدات المطلوبة</h2>
                    <p class="text-gray-600 mb-4">سيتم إنشاء مجلدات uploads و logs مع الصلاحيات المناسبة</p>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="create_directories">
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-300">
                            إنشاء المجلدات
                        </button>
                    </form>
                    
                <?php elseif ($step == 5): ?>
                    <h2 class="text-xl font-bold mb-4">🎉 تم الإعداد بنجاح!</h2>
                    <p class="text-gray-600 mb-6">تم إعداد النظام بنجاح. يمكنك الآن استخدام المدونة.</p>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <h3 class="font-bold text-blue-800 mb-2">معلومات تسجيل الدخول:</h3>
                        <p><strong>الرابط:</strong> <a href="admin/login.php" class="text-blue-600 hover:underline">admin/login.php</a></p>
                        <p><strong>اسم المستخدم:</strong> admin</p>
                        <p><strong>كلمة المرور:</strong> admin123</p>
                        <p class="text-red-600 font-semibold mt-2">⚠️ غيّر كلمة المرور فوراً!</p>
                    </div>
                    
                    <div class="space-y-3">
                        <a href="index.php" class="block w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                            الذهاب للصفحة الرئيسية
                        </a>
                        <a href="admin/login.php" class="block w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition duration-300 text-center">
                            الذهاب للوحة التحكم
                        </a>
                        <a href="test.php" class="block w-full bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition duration-300 text-center">
                            اختبار النظام
                        </a>
                    </div>
                    
                    <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <p class="text-yellow-800 font-semibold">⚠️ أمان مهم:</p>
                        <p class="text-yellow-700 text-sm mt-1">احذف ملف setup.php بعد الانتهاء من الإعداد لأسباب أمنية.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
